# Multi-Agent AI Trading Assistant Environment Variables
# Copy this file to .env and fill in your actual values

# PostgreSQL Database
POSTGRES_PASSWORD=crypto_trading_secure_2024
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=crypto_trading_ai
POSTGRES_USER=n8n_user

# n8n Configuration
N8N_USER=admin
N8N_PASSWORD=crypto_admin_2024
N8N_HOST=localhost
N8N_PORT=5678
N8N_DB=n8n
N8N_DB_USER=n8n_user
N8N_DB_PASSWORD=crypto_trading_secure_2024
TIMEZONE=Europe/Istanbul

# Redis Configuration
REDIS_PASSWORD=crypto_redis_2024

# pgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=crypto_pgadmin_2024

# API Keys (Fill these with your actual API keys)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

COINGECKO_API_KEY=your_coingecko_api_key_here

NEWSAPI_KEY=your_newsapi_key_here

OPENAI_API_KEY=your_openai_api_key_here

TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Twitter/X API (Optional)
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here

# CryptoPanic API (Optional)
CRYPTOPANIC_API_KEY=your_cryptopanic_api_key_here

# Trading Configuration
DEFAULT_TRADING_PAIRS=BTCUSDT,ETHUSDT,ADAUSDT,XRPUSDT,SOLUSDT
RISK_TOLERANCE=MEDIUM
MAX_DAILY_LOSS_PERCENTAGE=5
POSITION_SIZE_PERCENTAGE=10

# System Configuration
LOG_LEVEL=info
ENABLE_METRICS=true
WEBHOOK_TIMEOUT_MS=30000
API_RATE_LIMIT_PER_MINUTE=60
