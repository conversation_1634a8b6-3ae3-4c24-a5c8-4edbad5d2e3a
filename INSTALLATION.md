# 🛠️ Multi-Agent AI Trading Assistant - <PERSON><PERSON><PERSON><PERSON> Kurulum Rehberi

## 📋 Kurulum Öncesi Kontrol Listesi

### Gerekli Hesaplar ve API Anahtarları

- [ ] **Binance Hesabı** - API anahtarı (sadece okuma yetkisi)
- [ ] **CoinGecko Pro** - API anahtarı (opsiyonel, rate limit için)
- [ ] **NewsAPI** - Ücretsiz API anahtarı
- [ ] **OpenAI** - GPT-4 API anahtarı
- [ ] **Telegram Bot** - @BotFather'dan bot token'ı
- [ ] **Docker & Docker Compose** - Sisteminizde kurulu

### Sistem Gereksinimleri

- **RAM**: Minimum 4GB, Önerilen 8GB
- **Disk**: Minimum 10GB boş alan
- **İşletim Sistemi**: Linux, macOS, Windows (Docker destekli)
- **Network**: İnternet bağlantısı (API çağrıları için)

## 🚀 Adım Adım Kurulum

### 1. <PERSON><PERSON>yi İndi<PERSON>

```bash
# Repository'yi k<PERSON>n
git clone https://github.com/inkbytefo/n8n-factory.git
cd n8n-factory

# Dosya yapısını kontrol edin
ls -la
```

### 2. Environment Dosyasını Hazırlayın

```bash
# .env dosyasını oluşturun
cp .env.example .env

# Dosyayı düzenleyin
nano .env  # veya favori editörünüz
```

**Önemli:** Aşağıdaki değerleri mutlaka doldurun:

```env
# API Anahtarları (ZORUNLU)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here
NEWSAPI_KEY=your_newsapi_key_here
OPENAI_API_KEY=your_openai_api_key_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Opsiyonel
COINGECKO_API_KEY=your_coingecko_api_key_here
```

### 3. Telegram Bot Kurulumu

#### Bot Oluşturma:
1. Telegram'da @BotFather'ı bulun
2. `/newbot` komutunu gönderin
3. Bot adını ve kullanıcı adını belirleyin
4. Aldığınız token'ı `.env` dosyasına ekleyin

#### Chat ID Bulma:
```bash
# Bot'unuza bir mesaj gönderin, sonra:
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates

# Response'da "chat":{"id":123456789} değerini bulun
# Bu ID'yi TELEGRAM_CHAT_ID olarak .env'e ekleyin
```

### 4. Docker Compose ile Sistemi Başlatın

```bash
# Servisleri başlatın
docker-compose up -d

# Logları kontrol edin
docker-compose logs -f

# Servis durumlarını kontrol edin
docker-compose ps
```

Beklenen çıktı:
```
NAME                     STATUS
crypto_trading_n8n       Up
crypto_trading_postgres  Up
crypto_trading_redis     Up (opsiyonel)
crypto_trading_pgadmin   Up (opsiyonel)
```

### 5. Veritabanı Şemasını Yükleyin

```bash
# PostgreSQL container'ının hazır olmasını bekleyin (30 saniye)
sleep 30

# Şemayı yükleyin
docker exec -i crypto_trading_postgres psql -U n8n_user -d crypto_trading_ai < database/schema.sql

# Başarılı olduğunu kontrol edin
docker exec -it crypto_trading_postgres psql -U n8n_user -d crypto_trading_ai -c "SELECT COUNT(*) FROM agent_status;"
```

### 6. n8n Arayüzüne Erişim

1. Tarayıcınızda http://localhost:5678 adresine gidin
2. İlk kullanıcı hesabını oluşturun:
   - **Email**: <EMAIL>
   - **Password**: Güçlü bir şifre seçin
   - **First Name**: Admin
   - **Last Name**: User

### 7. Kimlik Bilgilerini Yapılandırın

n8n arayüzünde Settings > Credentials'a gidin ve aşağıdaki kimlik bilgilerini oluşturun:

#### PostgreSQL Credential (`crypto_trading_db`)
- **Type**: Postgres
- **Name**: crypto_trading_db
- **Host**: postgres
- **Database**: crypto_trading_ai
- **User**: n8n_user
- **Password**: crypto_trading_secure_2024
- **Port**: 5432

#### Binance API Credential (`binance_api`)
- **Type**: Generic Credential
- **Name**: binance_api
- **Fields**:
  - `api_key`: Binance API Key
  - `api_secret`: Binance API Secret

#### NewsAPI Credential (`newsapi_key`)
- **Type**: Generic Credential
- **Name**: newsapi_key
- **Fields**:
  - `api_key`: NewsAPI Key

#### OpenAI Credential (`openai_gpt4`)
- **Type**: OpenAI
- **Name**: openai_gpt4
- **API Key**: OpenAI API Key

#### Telegram Bot Credential (`telegram_bot`)
- **Type**: Generic Credential
- **Name**: telegram_bot
- **Fields**:
  - `bot_token`: Telegram Bot Token
  - `chat_id`: Telegram Chat ID

### 8. Workflow'ları İçe Aktarın ve Aktifleştirin

#### Otomatik İçe Aktarma (Önerilen):
```bash
# Workflow'ları otomatik olarak içe aktarın
./scripts/import-workflows.sh
```

#### Manuel İçe Aktarma:
1. n8n arayüzünde "Import from URL" veya "Import from file" kullanın
2. Her workflow için aşağıdaki sırayı takip edin:

**Sıralı Aktifleştirme:**
1. 🕵️ Data Harvester Agent
2. 📈 Technical Analyst Agent
3. 📰 Sentiment & News Analyst Agent
4. 🛡️ Risk Manager Agent
5. 🧠 Master Strategist Brain
6. 💬 Telegram Herald Assistant

### 9. Sistem Testini Yapın

#### Veritabanı Bağlantı Testi:
```bash
docker exec -it crypto_trading_postgres psql -U n8n_user -d crypto_trading_ai -c "SELECT version();"
```

#### API Bağlantı Testleri:
```bash
# Binance API
curl -X GET 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT'

# CoinGecko API
curl -X GET 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd'

# Fear & Greed Index
curl -X GET 'https://api.alternative.me/fng/'
```

#### Telegram Bot Testi:
1. Telegram'da bot'unuza `/start` mesajı gönderin
2. Yanıt alıp almadığınızı kontrol edin

### 10. İlk Çalıştırma ve Monitoring

#### Agent Durumlarını Kontrol Edin:
```sql
-- pgAdmin veya psql ile
SELECT agent_name, status, last_run, next_scheduled_run 
FROM agent_status 
ORDER BY agent_name;
```

#### n8n Execution Loglarını İzleyin:
1. n8n arayüzünde "Executions" sekmesine gidin
2. Her workflow'un düzenli çalıştığını kontrol edin

#### Telegram'da Sistem Durumunu Kontrol Edin:
```
/status  # Sistem durumu
/analiz  # İlk analiz raporu (15 dakika sonra)
```

## 🔧 Sorun Giderme

### Yaygın Sorunlar ve Çözümleri

#### 1. PostgreSQL Bağlantı Hatası
```bash
# Container'ı yeniden başlatın
docker-compose restart postgres

# Logları kontrol edin
docker-compose logs postgres
```

#### 2. API Rate Limit Hataları
- CoinGecko Pro API anahtarı kullanın
- Workflow çalışma sıklığını azaltın

#### 3. Telegram Bot Yanıt Vermiyor
- Bot token'ının doğru olduğunu kontrol edin
- Chat ID'nin doğru olduğunu kontrol edin
- Bot'un aktif olduğunu kontrol edin

#### 4. OpenAI API Hataları
- API anahtarının geçerli olduğunu kontrol edin
- Hesap bakiyenizi kontrol edin
- Rate limit'leri kontrol edin

### Log Dosyaları

```bash
# n8n logları
docker-compose logs n8n

# PostgreSQL logları
docker-compose logs postgres

# Tüm servis logları
docker-compose logs
```

## 📊 Performans Optimizasyonu

### Önerilen Ayarlar

1. **PostgreSQL Optimizasyonu**:
```sql
-- Performans için index'leri kontrol edin
EXPLAIN ANALYZE SELECT * FROM latest_market_data;
```

2. **n8n Memory Ayarları**:
```yaml
# docker-compose.yml'de
environment:
  - NODE_OPTIONS=--max-old-space-size=4096
```

3. **Workflow Execution Ayarları**:
- Timeout değerlerini artırın
- Retry mekanizmalarını aktifleştirin

## 🔄 Güncelleme

```bash
# Yeni versiyonu çekin
git pull origin main

# Servisleri yeniden başlatın
docker-compose down
docker-compose up -d

# Veritabanı migration'larını çalıştırın (varsa)
./scripts/migrate-database.sh
```

## 🆘 Destek

Sorun yaşıyorsanız:
1. Bu dokümandaki sorun giderme bölümünü kontrol edin
2. GitHub Issues'da benzer sorunları arayın
3. Yeni issue açın (log dosyalarını ekleyin)
4. inkbytefo ile iletişime geçin

---

**🎉 Tebrikler!** Multi-Agent AI Trading Assistant sisteminiz artık çalışıyor!
