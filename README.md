# 🚀 Multi-Agent AI Crypto Trading Assistant

**Author:** inkbytefo  
**Version:** 1.0.0  
**License:** MIT

## 🎯 Proje Özeti

Bu proje, n8n workflow automation platformu üzerinde çalışan çok-ajanlı bir kripto para trading asistanıdır. <PERSON><PERSON>m, birb<PERSON>leriyle haberleşen 6 farklı uzman ajandan oluşur ve kullanıcıya Telegram üzerinden proaktif trading tavsiyeleri sunar.

## 🏗️ Sistem Mimarisi

### 🤖 Ajanlar

1. **🕵️ Data Harvester Agent** (`zpYWv0xLbtfqq42h`)
   - Her 1 dakikada çalışır
   - Binance, CoinGecko, NewsAPI, Fear & Greed Index verilerini toplar
   - Ham verileri PostgreSQL'e kaydeder

2. **📈 Technical Analyst Agent** (`ZR1RuXRyoo24qSXo`)
   - Her 5 dakikada çalışır
   - RSI, MACD, Bollinger Bands hesaplar
   - Trend analizi ve pattern tespiti yapar

3. **📰 Sentiment & News Analyst Agent** (`TmwcCWa2fhDOu4b3`)
   - Her 2 dakikada çalışır
   - OpenAI GPT-4 ile haber duygu analizi
   - Yüksek etkili haberlerde Master Strategist'i tetikler

4. **🛡️ Risk Manager Agent** (`YXDdikbGO2eo34aL`)
   - Her 10 dakikada çalışır
   - Portföy risk değerlendirmesi
   - Volatilite analizi ve stop-loss önerileri

5. **🧠 Master Strategist Brain** (`REAXhppZ7mrHqT9K`)
   - Her 15 dakikada veya acil durumlarda çalışır
   - Tüm ajanların verilerini sentezler
   - AI destekli strateji önerileri üretir

6. **💬 Telegram Herald Assistant** (`DE5E93mVMBFjaM7z`)
   - Kullanıcı komutlarına yanıt verir
   - Proaktif fırsat bildirimleri gönderir
   - Sistem durumu ve analiz raporları sunar

### 🗄️ Veritabanı Şeması

PostgreSQL veritabanı 7 ana tablo içerir:
- `market_data`: Ham piyasa verileri
- `technical_analysis`: Teknik analiz sonuçları
- `news_and_sentiment`: Haber ve duygu analizi
- `risk_assessments`: Risk değerlendirmeleri
- `synthesis_reports`: Master strategist raporları
- `actionable_insights`: Eyleme dönük tavsiyeler
- `agent_status`: Ajan durumları ve sağlık bilgileri

## 🚀 Kurulum

### 1. Ön Gereksinimler

```bash
# Docker ve Docker Compose kurulu olmalı
docker --version
docker-compose --version

# Git repository'yi klonlayın
git clone <repository-url>
cd n8n-factory
```

### 2. Environment Dosyasını Hazırlayın

```bash
# .env dosyasını oluşturun
cp .env.example .env

# API anahtarlarınızı .env dosyasına ekleyin:
# - BINANCE_API_KEY
# - COINGECKO_API_KEY  
# - NEWSAPI_KEY
# - OPENAI_API_KEY
# - TELEGRAM_BOT_TOKEN
# - TELEGRAM_CHAT_ID
```

### 3. Sistemi Başlatın

```bash
# PostgreSQL ve n8n'i başlatın
docker-compose up -d

# Veritabanı şemasını yükleyin
docker exec -i crypto_trading_postgres psql -U n8n_user -d crypto_trading_ai < database/schema.sql
```

### 4. n8n Kimlik Bilgilerini Yapılandırın

1. http://localhost:5678 adresine gidin
2. Admin hesabı oluşturun
3. Settings > Credentials'da aşağıdaki kimlik bilgilerini ekleyin:
   - `crypto_trading_db` (PostgreSQL)
   - `binance_api` (Generic Credential)
   - `coingecko_api` (Generic Credential)
   - `newsapi_key` (Generic Credential)
   - `openai_gpt4` (OpenAI)
   - `telegram_bot` (Generic Credential)

### 5. Workflow'ları Aktifleştirin

Her bir workflow'u sırasıyla aktifleştirin:
1. 🕵️ Data Harvester Agent
2. 📈 Technical Analyst Agent  
3. 📰 Sentiment & News Analyst Agent
4. 🛡️ Risk Manager Agent
5. 🧠 Master Strategist Brain
6. 💬 Telegram Herald Assistant

## 📱 Kullanım

### Telegram Komutları

- `/start` - Hoşgeldin mesajı ve sistem tanıtımı
- `/status` - Tüm ajanların durumu ve sistem sağlığı
- `/analiz` - En son piyasa analizi raporu
- `/yardim` - Mevcut komutların listesi

### Proaktif Bildirimler

Sistem aşağıdaki durumlarda otomatik bildirim gönderir:
- Yüksek güvenilirlikli trading fırsatı tespit edildiğinde
- Kritik risk seviyesi aşıldığında
- Yüksek etkili haberler çıktığında

## 🔧 Sistem Monitoring

### Agent Status Kontrolü

```sql
SELECT agent_name, status, last_run, records_processed 
FROM agent_status 
ORDER BY last_run DESC;
```

### Son Analizleri Görüntüleme

```sql
SELECT market_scenario, confidence_score, created_at 
FROM synthesis_reports 
ORDER BY created_at DESC 
LIMIT 5;
```

## 🛠️ Geliştirme

### Yeni Ajan Ekleme

1. Yeni PostgreSQL tabloları oluşturun
2. n8n'de yeni workflow oluşturun
3. `agent_status` tablosuna yeni kayıt ekleyin
4. Master Strategist'in veri toplama sorgularını güncelleyin

### API Entegrasyonları

Sistem şu API'leri kullanır:
- **Binance API**: Gerçek zamanlı fiyat verileri
- **CoinGecko API**: Market cap ve hacim verileri
- **NewsAPI**: Kripto haberler
- **Alternative.me**: Fear & Greed Index
- **OpenAI API**: Duygu analizi ve strateji sentezi

## 🔒 Güvenlik

- API anahtarları n8n credential manager'da güvenli şekilde saklanır
- Binance API sadece okuma yetkisi ile kullanılır
- PostgreSQL şifreleri güçlü ve benzersizdir
- Telegram bot token'ı gizli tutulur

## 📊 Performans

- **Veri Toplama**: 1 dakikada bir
- **Teknik Analiz**: 5 dakikada bir
- **Duygu Analizi**: 2 dakikada bir
- **Risk Değerlendirmesi**: 10 dakikada bir
- **Strateji Sentezi**: 15 dakikada bir

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## 🆘 Destek

Sorunlar için GitHub Issues kullanın veya inkbytefo ile iletişime geçin.

---

**⚠️ Uyarı:** Bu sistem eğitim amaçlıdır. Gerçek trading kararları vermeden önce profesyonel finansal danışmanlık alın.
