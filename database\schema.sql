-- Multi-Agent AI Trading Assistant Database Schema
-- Author: inkbytefo
-- Description: PostgreSQL schema for the multi-agent crypto trading system

-- Create database (run this separately if needed)
-- CREATE DATABASE crypto_trading_ai;

-- Use the database
-- \c crypto_trading_ai;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for better data integrity
CREATE TYPE sentiment_score AS ENUM ('VERY_NEGATIVE', 'NEGATIVE', 'NEUTRAL', 'POSITIVE', 'VERY_POSITIVE');
CREATE TYPE market_impact AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL');
CREATE TYPE risk_level AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'DANGER');
CREATE TYPE signal_type AS ENUM ('BUY', 'SELL', 'HOLD', 'STRONG_BUY', 'STRONG_SELL');

-- Table 1: Market Data (Raw price, volume, etc.)
CREATE TABLE market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    price DECIMAL(20, 8) NOT NULL,
    volume_24h DECIMAL(20, 8),
    market_cap DECIMAL(20, 2),
    price_change_24h DECIMAL(10, 4),
    price_change_percentage_24h DECIMAL(10, 4),
    fear_greed_index INTEGER CHECK (fear_greed_index >= 0 AND fear_greed_index <= 100),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(50) NOT NULL, -- 'binance', 'coingecko', etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table 2: Technical Analysis Results
CREATE TABLE technical_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    timeframe VARCHAR(10) NOT NULL, -- '1m', '5m', '15m', '1h', '4h', '1d'
    rsi DECIMAL(5, 2),
    macd_line DECIMAL(10, 6),
    macd_signal DECIMAL(10, 6),
    macd_histogram DECIMAL(10, 6),
    bb_upper DECIMAL(20, 8),
    bb_middle DECIMAL(20, 8),
    bb_lower DECIMAL(20, 8),
    bb_squeeze BOOLEAN,
    support_level DECIMAL(20, 8),
    resistance_level DECIMAL(20, 8),
    trend_direction VARCHAR(20), -- 'BULLISH', 'BEARISH', 'SIDEWAYS'
    pattern_detected VARCHAR(100), -- 'HEAD_AND_SHOULDERS', 'DOUBLE_TOP', etc.
    signal signal_type,
    confidence_score DECIMAL(3, 2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table 3: News and Sentiment Analysis
CREATE TABLE news_and_sentiment (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    content TEXT,
    url TEXT,
    source VARCHAR(100) NOT NULL, -- 'newsapi', 'cryptopanic', 'twitter', etc.
    author VARCHAR(200),
    published_at TIMESTAMP WITH TIME ZONE,
    sentiment sentiment_score,
    sentiment_confidence DECIMAL(3, 2) CHECK (sentiment_confidence >= 0 AND sentiment_confidence <= 1),
    market_impact market_impact,
    mentioned_symbols TEXT[], -- Array of crypto symbols mentioned
    key_entities TEXT[], -- Array of important entities (SEC, ETF, etc.)
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table 4: Risk Assessments
CREATE TABLE risk_assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_value DECIMAL(20, 8),
    total_pnl DECIMAL(20, 8),
    daily_pnl DECIMAL(20, 8),
    open_positions INTEGER,
    portfolio_risk_score risk_level,
    market_volatility DECIMAL(5, 2), -- VIX-like score
    max_drawdown DECIMAL(5, 2),
    risk_reward_ratio DECIMAL(5, 2),
    position_sizes JSONB, -- JSON object with symbol: size pairs
    stop_loss_levels JSONB, -- JSON object with symbol: stop_loss pairs
    recommendations TEXT[],
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table 5: Synthesis Reports (Master strategist outputs)
CREATE TABLE synthesis_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_scenario TEXT NOT NULL,
    alternative_scenario TEXT,
    confidence_score DECIMAL(3, 2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    technical_summary TEXT,
    sentiment_summary TEXT,
    risk_summary TEXT,
    conflicting_signals TEXT[],
    historical_patterns TEXT,
    raw_analysis_data JSONB, -- Full AI response for audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table 6: Actionable Insights (Ready-to-send recommendations)
CREATE TABLE actionable_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    action signal_type NOT NULL,
    confidence_score DECIMAL(3, 2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    target_price DECIMAL(20, 8),
    stop_loss DECIMAL(20, 8),
    potential_return_min DECIMAL(5, 2),
    potential_return_max DECIMAL(5, 2),
    time_horizon VARCHAR(20), -- '1h', '4h', '1d', '1w', etc.
    reasoning TEXT NOT NULL,
    supporting_data JSONB,
    sent_to_user BOOLEAN DEFAULT FALSE,
    user_response VARCHAR(50), -- 'ACCEPTED', 'REJECTED', 'IGNORED'
    synthesis_report_id UUID REFERENCES synthesis_reports(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table 7: Agent Status and Health Monitoring
CREATE TABLE agent_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_name VARCHAR(100) NOT NULL,
    last_run TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) NOT NULL, -- 'RUNNING', 'IDLE', 'ERROR', 'DISABLED'
    error_message TEXT,
    execution_time_ms INTEGER,
    records_processed INTEGER,
    next_scheduled_run TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_market_data_symbol_timestamp ON market_data(symbol, timestamp DESC);
CREATE INDEX idx_technical_analysis_symbol_timeframe ON technical_analysis(symbol, timeframe, timestamp DESC);
CREATE INDEX idx_news_sentiment_published ON news_and_sentiment(published_at DESC);
CREATE INDEX idx_news_sentiment_processed ON news_and_sentiment(processed);
CREATE INDEX idx_risk_assessments_timestamp ON risk_assessments(timestamp DESC);
CREATE INDEX idx_actionable_insights_sent ON actionable_insights(sent_to_user, created_at DESC);
CREATE INDEX idx_agent_status_name ON agent_status(agent_name);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_agent_status_updated_at 
    BEFORE UPDATE ON agent_status 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial agent status records
INSERT INTO agent_status (agent_name, status, next_scheduled_run) VALUES
('data_harvester', 'IDLE', CURRENT_TIMESTAMP + INTERVAL '1 minute'),
('technical_analyst', 'IDLE', CURRENT_TIMESTAMP + INTERVAL '5 minutes'),
('sentiment_analyst', 'IDLE', CURRENT_TIMESTAMP + INTERVAL '2 minutes'),
('risk_manager', 'IDLE', CURRENT_TIMESTAMP + INTERVAL '10 minutes'),
('master_strategist', 'IDLE', CURRENT_TIMESTAMP + INTERVAL '15 minutes'),
('telegram_herald', 'IDLE', CURRENT_TIMESTAMP);

-- Create a view for latest market data per symbol
CREATE VIEW latest_market_data AS
SELECT DISTINCT ON (symbol) 
    symbol, price, volume_24h, price_change_percentage_24h, 
    fear_greed_index, timestamp, source
FROM market_data 
ORDER BY symbol, timestamp DESC;

-- Create a view for latest technical analysis per symbol/timeframe
CREATE VIEW latest_technical_analysis AS
SELECT DISTINCT ON (symbol, timeframe) 
    symbol, timeframe, rsi, macd_line, macd_signal, 
    trend_direction, signal, confidence_score, timestamp
FROM technical_analysis 
ORDER BY symbol, timeframe, timestamp DESC;

-- Grant permissions (adjust as needed for your n8n user)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO n8n_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO n8n_user;
