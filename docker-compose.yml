version: '3.8'

services:
  # PostgreSQL Database for the Multi-Agent System
  postgres:
    image: postgres:15-alpine
    container_name: crypto_trading_postgres
    environment:
      POSTGRES_DB: crypto_trading_ai
      POSTGRES_USER: n8n_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-crypto_trading_secure_2024}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./database/seed-data.sql:/docker-entrypoint-initdb.d/02-seed-data.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n_user -d crypto_trading_ai"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - crypto_trading_network

  # n8n Workflow Automation Platform
  n8n:
    image: n8nio/n8n:latest
    container_name: crypto_trading_n8n
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD:-crypto_admin_2024}
      - N8N_HOST=${N8N_HOST:-localhost}
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=${TIMEZONE:-Europe/Istanbul}
      - N8N_METRICS=true
      - N8N_LOG_LEVEL=info
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=${N8N_DB:-n8n}
      - DB_POSTGRESDB_USER=${N8N_DB_USER:-n8n_user}
      - DB_POSTGRESDB_PASSWORD=${N8N_DB_PASSWORD:-crypto_trading_secure_2024}
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
      - ./credentials:/home/<USER>/.n8n/credentials
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - crypto_trading_network

  # Redis for caching and session management (optional but recommended)
  redis:
    image: redis:7-alpine
    container_name: crypto_trading_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-crypto_redis_2024}
    networks:
      - crypto_trading_network

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: crypto_trading_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-crypto_pgadmin_2024}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    restart: unless-stopped
    depends_on:
      - postgres
    networks:
      - crypto_trading_network

volumes:
  postgres_data:
    driver: local
  n8n_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  crypto_trading_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
